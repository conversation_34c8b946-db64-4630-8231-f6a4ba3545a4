#!/bin/bash

# Test script for ngrok HTTPS setup
echo "🧪 Testing ngrok HTTPS setup for n8n..."
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get ngrok URL from environment
NGROK_URL="https://fce761a72592.ngrok-free.app"

echo "🔗 Testing ngrok URL: $NGROK_URL"
echo ""

# Test 1: Basic connectivity
echo "1️⃣ Testing basic HTTPS connectivity..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$NGROK_URL")
if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ HTTPS connectivity: OK (HTTP $HTTP_CODE)${NC}"
else
    echo -e "${RED}❌ HTTPS connectivity: FAILED (HTTP $HTTP_CODE)${NC}"
fi

# Test 2: Health endpoint
echo ""
echo "2️⃣ Testing n8n health endpoint..."
HEALTH_RESPONSE=$(curl -s "$NGROK_URL/healthz")
if [[ "$HEALTH_RESPONSE" == *"ok"* ]]; then
    echo -e "${GREEN}✅ n8n health check: OK${NC}"
    echo "   Response: $HEALTH_RESPONSE"
else
    echo -e "${RED}❌ n8n health check: FAILED${NC}"
    echo "   Response: $HEALTH_RESPONSE"
fi

# Test 3: OAuth callback endpoint
echo ""
echo "3️⃣ Testing OAuth callback endpoint..."
OAUTH_URL="$NGROK_URL/rest/oauth2-credential/callback"
OAUTH_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$OAUTH_URL")
if [ "$OAUTH_CODE" = "400" ] || [ "$OAUTH_CODE" = "404" ] || [ "$OAUTH_CODE" = "200" ]; then
    echo -e "${GREEN}✅ OAuth endpoint accessible: OK (HTTP $OAUTH_CODE)${NC}"
    echo "   URL: $OAUTH_URL"
else
    echo -e "${RED}❌ OAuth endpoint: FAILED (HTTP $OAUTH_CODE)${NC}"
fi

# Test 4: ngrok tunnel status
echo ""
echo "4️⃣ Checking ngrok tunnel status..."
NGROK_API=$(curl -s http://localhost:4040/api/tunnels)
if [[ "$NGROK_API" == *"https"* ]]; then
    echo -e "${GREEN}✅ ngrok tunnel: ACTIVE${NC}"
    echo "   Dashboard: http://localhost:4040"
else
    echo -e "${RED}❌ ngrok tunnel: NOT ACTIVE${NC}"
fi

echo ""
echo "📋 Summary:"
echo "   • ngrok URL: $NGROK_URL"
echo "   • OAuth Redirect URL: $OAUTH_URL"
echo "   • ngrok Dashboard: http://localhost:4040"
echo ""
echo -e "${YELLOW}🔧 Next steps:${NC}"
echo "   1. Update your Slack app OAuth redirect URL to: $OAUTH_URL"
echo "   2. Test Slack OAuth in n8n credentials"
echo "   3. Deploy your ALSA workflow"