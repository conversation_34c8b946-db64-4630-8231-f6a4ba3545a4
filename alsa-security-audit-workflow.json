{"name": "ALSA Security & Audit System", "nodes": [{"id": "security-webhook", "name": "Security Event Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [100, 400], "parameters": {"path": "alsa-security", "httpMethod": "POST", "responseMode": "lastNode", "responseData": "firstEntryJson"}, "onError": "continueRegularOutput", "alwaysOutputData": true}, {"id": "process-security-event", "name": "Process Security Event", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [300, 400], "parameters": {"language": "javaScript", "jsCode": "// Process and classify security events\nconst eventData = items[0].json;\n\n// Extract event information\nconst eventType = eventData.type || 'unknown';\nconst severity = eventData.severity || 'medium';\nconst source = eventData.source || 'system';\nconst userId = eventData.userId || 'anonymous';\nconst ipAddress = eventData.ipAddress || 'unknown';\nconst userAgent = eventData.userAgent || 'unknown';\nconst timestamp = eventData.timestamp || DateTime.now().toISO();\nconst details = eventData.details || {};\n\n// Security event classification\nconst securityEvents = {\n  'failed_auth': {\n    severity: 'high',\n    category: 'authentication',\n    description: 'Failed authentication attempt',\n    alertThreshold: 5\n  },\n  'suspicious_activity': {\n    severity: 'high',\n    category: 'behavior',\n    description: 'Suspicious user behavior detected',\n    alertThreshold: 1\n  },\n  'rate_limit_exceeded': {\n    severity: 'medium',\n    category: 'abuse',\n    description: 'Rate limit exceeded',\n    alertThreshold: 10\n  },\n  'unauthorized_access': {\n    severity: 'critical',\n    category: 'access',\n    description: 'Unauthorized access attempt',\n    alertThreshold: 1\n  },\n  'data_breach_attempt': {\n    severity: 'critical',\n    category: 'data',\n    description: 'Potential data breach attempt',\n    alertThreshold: 1\n  },\n  'malicious_content': {\n    severity: 'high',\n    category: 'content',\n    description: 'Malicious content detected',\n    alertThreshold: 1\n  },\n  'system_anomaly': {\n    severity: 'medium',\n    category: 'system',\n    description: 'System anomaly detected',\n    alertThreshold: 3\n  }\n};\n\n// Get event classification\nconst eventClassification = securityEvents[eventType] || {\n  severity: 'low',\n  category: 'unknown',\n  description: 'Unknown security event',\n  alertThreshold: 10\n};\n\n// Generate unique event ID\nconst eventId = `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n// Risk scoring\nlet riskScore = 0;\nswitch (eventClassification.severity) {\n  case 'critical': riskScore = 100; break;\n  case 'high': riskScore = 75; break;\n  case 'medium': riskScore = 50; break;\n  case 'low': riskScore = 25; break;\n  default: riskScore = 10;\n}\n\n// Adjust risk score based on factors\nif (details.repeatedAttempts) riskScore += 20;\nif (details.fromTorNetwork) riskScore += 30;\nif (details.suspiciousUserAgent) riskScore += 15;\nif (details.officeHours === false) riskScore += 10;\nif (details.knownMaliciousIP) riskScore += 50;\n\n// Cap at 100\nriskScore = Math.min(riskScore, 100);\n\n// Geolocation analysis (mock)\nconst geoLocation = {\n  country: details.country || 'Unknown',\n  city: details.city || 'Unknown',\n  isVPN: details.isVPN || false,\n  isTor: details.isTor || false,\n  isDataCenter: details.isDataCenter || false\n};\n\n// Create comprehensive security event\nconst securityEvent = {\n  eventId: eventId,\n  timestamp: timestamp,\n  type: eventType,\n  classification: eventClassification,\n  severity: eventClassification.severity,\n  category: eventClassification.category,\n  riskScore: riskScore,\n  \n  source: {\n    system: source,\n    ipAddress: ipAddress,\n    userAgent: userAgent,\n    geoLocation: geoLocation\n  },\n  \n  user: {\n    id: userId,\n    isAuthenticated: details.isAuthenticated || false,\n    role: details.userRole || 'unknown',\n    lastSeen: details.lastSeen || null\n  },\n  \n  context: {\n    endpoint: details.endpoint || null,\n    method: details.method || null,\n    payload: details.payload || null,\n    referrer: details.referrer || null,\n    sessionId: details.sessionId || null\n  },\n  \n  analysis: {\n    isAnomaly: riskScore > 70,\n    requiresAlert: riskScore > eventClassification.alertThreshold * 10,\n    requiresBlocking: riskScore > 90,\n    confidence: details.confidence || 0.8\n  },\n  \n  response: {\n    action: riskScore > 90 ? 'block' : riskScore > 70 ? 'monitor' : 'log',\n    alertSent: false,\n    blocked: false,\n    investigated: false\n  },\n  \n  metadata: {\n    detectionMethod: details.detectionMethod || 'rule-based',\n    processingTime: Date.now() - new Date(timestamp).getTime(),\n    version: '1.0'\n  }\n};\n\nreturn [{ json: securityEvent }];"}}, {"id": "threat-intelligence", "name": "Threat Intelligence Check", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 400], "parameters": {"language": "javaScript", "jsCode": "// Threat intelligence and reputation checking\nconst securityEvent = items[0].json;\nconst ipAddress = securityEvent.source.ipAddress;\nconst userAgent = securityEvent.source.userAgent;\n\n// Mock threat intelligence database\nconst threatIntelligence = {\n  maliciousIPs: [\n    '*************', // Example malicious IP\n    '*********'\n  ],\n  suspiciousUserAgents: [\n    'curl/',\n    'python-requests/',\n    'bot',\n    'crawler'\n  ],\n  knownAttackPatterns: [\n    'sql injection',\n    'xss attempt',\n    'directory traversal',\n    'command injection'\n  ]\n};\n\n// Check IP reputation\nconst ipThreatLevel = threatIntelligence.maliciousIPs.includes(ipAddress) ? 'high' : 'low';\n\n// Check user agent\nconst isAutomatedTool = threatIntelligence.suspiciousUserAgents.some(pattern => \n  userAgent.toLowerCase().includes(pattern)\n);\n\n// Pattern analysis\nconst contentAnalysis = {\n  containsSQLInjection: false,\n  containsXSS: false,\n  containsDirectoryTraversal: false,\n  containsCommandInjection: false\n};\n\nif (securityEvent.context.payload) {\n  const payload = JSON.stringify(securityEvent.context.payload).toLowerCase();\n  contentAnalysis.containsSQLInjection = /('|(\\-\\-)|(;)|(\\||\\|)|(\\*|\\*))/.test(payload);\n  contentAnalysis.containsXSS = /<script|javascript:|on\\w+=/i.test(payload);\n  contentAnalysis.containsDirectoryTraversal = /\\.\\.[\\/\\\\]/.test(payload);\n  contentAnalysis.containsCommandInjection = /(\\||&|;|\\$|`|<|>)/.test(payload);\n}\n\n// Calculate threat score\nlet threatScore = 0;\nif (ipThreatLevel === 'high') threatScore += 40;\nif (isAutomatedTool) threatScore += 20;\nif (contentAnalysis.containsSQLInjection) threatScore += 30;\nif (contentAnalysis.containsXSS) threatScore += 30;\nif (contentAnalysis.containsDirectoryTraversal) threatScore += 25;\nif (contentAnalysis.containsCommandInjection) threatScore += 35;\n\n// Update security event with threat intelligence\nconst enhancedEvent = {\n  ...securityEvent,\n  threatIntelligence: {\n    ipReputation: ipThreatLevel,\n    isAutomatedTool: isAutomatedTool,\n    threatScore: threatScore,\n    contentAnalysis: contentAnalysis,\n    riskFactors: [\n      ...(ipThreatLevel === 'high' ? ['malicious_ip'] : []),\n      ...(isAutomatedTool ? ['automated_tool'] : []),\n      ...(contentAnalysis.containsSQLInjection ? ['sql_injection'] : []),\n      ...(contentAnalysis.containsXSS ? ['xss_attempt'] : []),\n      ...(contentAnalysis.containsDirectoryTraversal ? ['directory_traversal'] : []),\n      ...(contentAnalysis.containsCommandInjection ? ['command_injection'] : [])\n    ]\n  }\n};\n\n// Update overall risk score\nenhancedEvent.riskScore = Math.min(enhancedEvent.riskScore + threatScore, 100);\n\n// Update response actions based on threat intelligence\nif (threatScore > 50) {\n  enhancedEvent.response.action = 'block';\n  enhancedEvent.analysis.requiresBlocking = true;\n}\n\nreturn [{ json: enhancedEvent }];"}}, {"id": "log-security-event", "name": "Log Security Event", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, 300], "parameters": {"language": "javaScript", "jsCode": "// Log security event to audit trail\nconst securityEvent = items[0].json;\n\n// Create audit log entry\nconst auditLogEntry = {\n  logId: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n  timestamp: DateTime.now().toISO(),\n  eventType: 'security_event',\n  severity: securityEvent.severity,\n  \n  event: {\n    id: securityEvent.eventId,\n    type: securityEvent.type,\n    riskScore: securityEvent.riskScore,\n    threatScore: securityEvent.threatIntelligence?.threatScore || 0\n  },\n  \n  source: securityEvent.source,\n  user: securityEvent.user,\n  context: securityEvent.context,\n  \n  response: securityEvent.response,\n  \n  compliance: {\n    gdprRelevant: securityEvent.user.id !== 'anonymous',\n    dataProcessed: !!securityEvent.context.payload,\n    retentionPeriod: '2 years',\n    classification: 'security'\n  },\n  \n  forensics: {\n    preserved: securityEvent.riskScore > 70,\n    hash: require('crypto').createHash('sha256')\n      .update(JSON.stringify(securityEvent))\n      .digest('hex'),\n    chainOfCustody: [\n      {\n        action: 'created',\n        timestamp: DateTime.now().toISO(),\n        system: 'ALSA Security System'\n      }\n    ]\n  }\n};\n\n// Store in audit database (mock)\nconsole.log('AUDIT LOG:', JSON.stringify(auditLogEntry, null, 2));\n\n// Return both original event and audit entry\nreturn [{ \n  json: {\n    securityEvent: securityEvent,\n    auditLog: auditLogEntry,\n    logged: true\n  }\n}];"}}, {"id": "check-alert-threshold", "name": "<PERSON> <PERSON><PERSON>", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [700, 500], "parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.securityEvent.analysis.requiresAlert }}", "rightValue": true, "operator": {"type": "boolean", "operation": "true"}}]}}}, {"id": "send-security-alert", "name": "Send Security Alert", "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [900, 400], "parameters": {"resource": "message", "operation": "post", "select": "channel", "channelId": "{{ $env.SLACK_SECURITY_CHANNEL }}", "text": "🚨 *SECURITY ALERT* 🚨\\n\\n**Event Type:** {{ $json.securityEvent.type }}\\n**Severity:** {{ $json.securityEvent.severity }}\\n**Risk Score:** {{ $json.securityEvent.riskScore }}/100\\n**Threat Score:** {{ $json.securityEvent.threatIntelligence.threatScore }}/100\\n\\n**Source IP:** {{ $json.securityEvent.source.ipAddress }}\\n**User:** {{ $json.securityEvent.user.id }}\\n**Action Taken:** {{ $json.securityEvent.response.action }}\\n\\n**Event ID:** {{ $json.securityEvent.eventId }}", "attachments": [{"color": "{{ $json.securityEvent.severity === 'critical' ? '#ff0000' : $json.securityEvent.severity === 'high' ? '#ff6600' : '#ffcc00' }}", "title": "Security Event Details", "fields": {"item": [{"title": "Classification", "value": "{{ $json.securityEvent.classification.category }}", "short": true}, {"title": "Detection Method", "value": "{{ $json.securityEvent.metadata.detectionMethod }}", "short": true}, {"title": "Geo Location", "value": "{{ $json.securityEvent.source.geoLocation.country }}, {{ $json.securityEvent.source.geoLocation.city }}", "short": true}, {"title": "Risk Factors", "value": "{{ $json.securityEvent.threatIntelligence.riskFactors.join(', ') }}", "short": false}]}}]}, "onError": "continueRegularOutput", "retryOnFail": true, "maxTries": 2}, {"id": "auto-response-action", "name": "Auto Response Action", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 600], "parameters": {"language": "javaScript", "jsCode": "// Execute automated response actions\nconst data = items[0].json;\nconst securityEvent = data.securityEvent;\nconst action = securityEvent.response.action;\n\nconst responseActions = {\n  block: {\n    description: 'Block IP address and user',\n    actions: [\n      'Add IP to firewall blacklist',\n      'Suspend user account',\n      'Invalidate active sessions',\n      'Log incident for investigation'\n    ]\n  },\n  monitor: {\n    description: 'Increase monitoring for user/IP',\n    actions: [\n      'Add to watch list',\n      'Increase logging level',\n      'Flag for manual review',\n      'Set up additional alerts'\n    ]\n  },\n  log: {\n    description: 'Log event for analysis',\n    actions: [\n      'Store in security database',\n      'Update threat intelligence',\n      'Add to analytics pipeline'\n    ]\n  }\n};\n\nconst selectedResponse = responseActions[action] || responseActions.log;\n\n// Simulate executing response actions\nconst executionResults = selectedResponse.actions.map(actionItem => ({\n  action: actionItem,\n  status: 'completed',\n  timestamp: DateTime.now().toISO(),\n  result: 'success'\n}));\n\n// Update security event with response results\nconst updatedEvent = {\n  ...securityEvent,\n  response: {\n    ...securityEvent.response,\n    executed: true,\n    executionTime: DateTime.now().toISO(),\n    actions: executionResults,\n    automated: true\n  }\n};\n\n// Create incident record if high severity\nlet incidentRecord = null;\nif (securityEvent.severity === 'critical' || securityEvent.severity === 'high') {\n  incidentRecord = {\n    incidentId: `inc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n    title: `Security Incident: ${securityEvent.type}`,\n    severity: securityEvent.severity,\n    status: 'open',\n    assignee: 'security-team',\n    createdAt: DateTime.now().toISO(),\n    relatedEvents: [securityEvent.eventId],\n    description: `Automated security incident created for ${securityEvent.type} event with risk score ${securityEvent.riskScore}`,\n    tags: ['automated', 'security', securityEvent.category]\n  };\n}\n\nreturn [{\n  json: {\n    securityEvent: updatedEvent,\n    responseExecuted: true,\n    incidentCreated: !!incidentRecord,\n    incident: incidentRecord,\n    summary: {\n      eventId: securityEvent.eventId,\n      action: action,\n      actionsExecuted: executionResults.length,\n      alertSent: data.alertSent || false,\n      blocked: action === 'block',\n      timestamp: DateTime.now().toISO()\n    }\n  }\n}];"}}], "connections": {"Security Event Webhook": {"main": [[{"node": "Process Security Event", "type": "main", "index": 0}]]}, "Process Security Event": {"main": [[{"node": "Threat Intelligence Check", "type": "main", "index": 0}]]}, "Threat Intelligence Check": {"main": [[{"node": "Log Security Event", "type": "main", "index": 0}, {"node": "<PERSON> <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Check Alert Threshold": {"main": [[{"node": "Send Security Alert", "type": "main", "index": 0}], [{"node": "Auto Response Action", "type": "main", "index": 0}]]}, "Send Security Alert": {"main": [[{"node": "Auto Response Action", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "saveManualExecutions": true, "timezone": "Asia/Hong_Kong"}}