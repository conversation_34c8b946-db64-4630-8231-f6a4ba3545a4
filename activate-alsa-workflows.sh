#!/bin/bash

# Script to activate ALSA workflows
# This script activates the workflows so they can receive webhook requests

set -e

# Configuration
N8N_URL="http://localhost:5678"
N8N_USER="Paul"
N8N_PASSWORD="Super-safe-password1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 Activating ALSA Workflows${NC}"
echo "==============================="

# Function to activate a workflow
activate_workflow() {
    local workflow_id=$1
    local workflow_name=$2
    
    echo -e "${YELLOW}Activating: ${workflow_name} (${workflow_id})${NC}"
    
    # Try to activate the workflow
    local response=$(curl -s \
        -X PATCH "${N8N_URL}/rest/workflows/${workflow_id}" \
        -H "Content-Type: application/json" \
        -H "Authorization: Basic $(echo -n "${N8N_USER}:${N8N_PASSWORD}" | base64)" \
        -d '{"active": true}' \
        2>/dev/null || echo '{"error": "Request failed"}')
    
    # Check if activation was successful
    if echo "$response" | grep -q '"active":true'; then
        echo -e "${GREEN}  ✅ Successfully activated${NC}"
        return 0
    else
        echo -e "${RED}  ❌ Failed to activate${NC}"
        echo -e "${RED}     Response: $response${NC}"
        return 1
    fi
}

# Function to get workflow list and activate ALSA workflows
activate_alsa_workflows() {
    echo -e "${YELLOW}Getting workflow list...${NC}"
    
    # Get list of workflows
    local workflows=$(curl -s \
        -X GET "${N8N_URL}/rest/workflows" \
        -H "Authorization: Basic $(echo -n "${N8N_USER}:${N8N_PASSWORD}" | base64)" \
        2>/dev/null || echo '{"data": []}')
    
    # Check if we got a valid response
    if ! echo "$workflows" | jq . >/dev/null 2>&1; then
        echo -e "${RED}❌ Failed to get workflow list${NC}"
        return 1
    fi
    
    # Extract ALSA workflow IDs and names
    local alsa_workflows=$(echo "$workflows" | jq -r '.data[] | select(.name | test("ALSA")) | "\(.id)|\(.name)"')
    
    if [ -z "$alsa_workflows" ]; then
        echo -e "${YELLOW}⚠️  No ALSA workflows found${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Found ALSA workflows:${NC}"
    echo "$alsa_workflows" | while IFS='|' read -r id name; do
        echo -e "  - ${name} (${id})"
    done
    
    echo ""
    
    # Activate each ALSA workflow
    local success_count=0
    local total_count=0
    
    echo "$alsa_workflows" | while IFS='|' read -r id name; do
        if activate_workflow "$id" "$name"; then
            ((success_count++))
        fi
        ((total_count++))
        echo ""
    done
    
    echo -e "${BLUE}Activation Summary:${NC}"
    echo -e "${GREEN}✅ Successfully activated: ${success_count}/${total_count}${NC}"
}

# Function to check n8n connectivity
check_n8n() {
    echo -e "${YELLOW}Checking n8n connectivity...${NC}"
    if curl -s "${N8N_URL}/healthz" | grep -q "ok"; then
        echo -e "${GREEN}✅ n8n is running${NC}"
        return 0
    else
        echo -e "${RED}❌ n8n is not accessible at ${N8N_URL}${NC}"
        return 1
    fi
}

# Function to test activated workflows
test_activated_workflows() {
    echo -e "${BLUE}🧪 Testing Activated Workflows${NC}"
    echo "==============================="
    
    # Test simple workflow
    echo -e "${YELLOW}Testing alsa-test workflow...${NC}"
    local test_response=$(curl -s -X POST "${N8N_URL}/webhook/alsa-test" \
        -H "Content-Type: application/json" \
        -d '{"text": "This is a test message for ALSA validation", "channel_id": "test", "user_id": "test"}' \
        2>/dev/null || echo '{"error": "Request failed"}')
    
    if echo "$test_response" | jq -r '.success' | grep -q "true"; then
        echo -e "${GREEN}  ✅ alsa-test is working!${NC}"
        local title=$(echo "$test_response" | jq -r '.alsa.title')
        local decision=$(echo "$test_response" | jq -r '.alsa.decision')
        echo -e "     Title: ${title}"
        echo -e "     Decision: ${decision}"
    else
        echo -e "${RED}  ❌ alsa-test failed${NC}"
        echo -e "     Response: $test_response"
    fi
    
    echo ""
    
    # Test complete workflow
    echo -e "${YELLOW}Testing alsa-complete workflow...${NC}"
    local test_response2=$(curl -s -X POST "${N8N_URL}/webhook/alsa-complete" \
        -H "Content-Type: application/json" \
        -d '{"text": "This is a comprehensive test message for the complete ALSA implementation with sufficient content to demonstrate the full processing capabilities", "channel_id": "test", "user_id": "test"}' \
        2>/dev/null || echo '{"error": "Request failed"}')
    
    if echo "$test_response2" | jq -r '.success' | grep -q "true"; then
        echo -e "${GREEN}  ✅ alsa-complete is working!${NC}"
        local title2=$(echo "$test_response2" | jq -r '.alsa.title')
        local decision2=$(echo "$test_response2" | jq -r '.alsa.decision')
        local category=$(echo "$test_response2" | jq -r '.alsa.category')
        echo -e "     Title: ${title2}"
        echo -e "     Decision: ${decision2}"
        echo -e "     Category: ${category}"
    else
        echo -e "${RED}  ❌ alsa-complete failed${NC}"
        echo -e "     Response: $test_response2"
    fi
}

# Main execution
main() {
    case "${1:-activate}" in
        "activate")
            check_n8n || exit 1
            echo ""
            activate_alsa_workflows
            echo ""
            test_activated_workflows
            echo ""
            echo -e "${GREEN}🎉 ALSA workflows are now active and ready to use!${NC}"
            echo ""
            echo -e "${BLUE}Webhook URLs:${NC}"
            echo -e "${CYAN}  Simple Test: ${N8N_URL}/webhook/alsa-test${NC}"
            echo -e "${CYAN}  Complete:    ${N8N_URL}/webhook/alsa-complete${NC}"
            ;;
        "test")
            check_n8n || exit 1
            test_activated_workflows
            ;;
        "help")
            echo "ALSA Workflow Activation Script"
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  activate - Activate ALSA workflows and test them (default)"
            echo "  test     - Test already activated workflows"
            echo "  help     - Show this help"
            ;;
        *)
            echo "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
