#!/bin/bash

# ALSA Workflow Deployment Script
# This script deploys the ALSA Slack-to-Notion automation workflow to your n8n instance

set -e

# Configuration
N8N_URL="http://localhost:5678"
N8N_USER="Paul"
N8N_PASSWORD="Super-safe-password1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 ALSA Workflow Deployment Script${NC}"
echo "=================================="

# Function to check if n8n is running
check_n8n() {
    echo -e "${YELLOW}Checking n8n health...${NC}"
    if curl -s "${N8N_URL}/healthz" | grep -q "ok"; then
        echo -e "${GREEN}✅ n8n is running and healthy${NC}"
        return 0
    else
        echo -e "${RED}❌ n8n is not accessible at ${N8N_URL}${NC}"
        return 1
    fi
}

# Function to get authentication cookie
get_auth_cookie() {
    echo -e "${YELLOW}Authenticating with n8n...${NC}"
    
    # Get login page to extract CSRF token
    LOGIN_PAGE=$(curl -s -c cookies.txt "${N8N_URL}/")
    
    # Try to login (n8n might not require auth in this setup)
    AUTH_RESPONSE=$(curl -s -b cookies.txt -c cookies.txt \
        -X POST "${N8N_URL}/rest/login" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"${N8N_USER}\",\"password\":\"${N8N_PASSWORD}\"}" \
        2>/dev/null || echo "")
    
    if [ -f cookies.txt ]; then
        echo -e "${GREEN}✅ Authentication successful${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  No authentication required or using basic auth${NC}"
        return 0
    fi
}

# Function to deploy workflow
deploy_workflow() {
    local workflow_file=$1
    local workflow_name=$2
    
    echo -e "${YELLOW}Deploying ${workflow_name}...${NC}"
    
    if [ ! -f "$workflow_file" ]; then
        echo -e "${RED}❌ Workflow file ${workflow_file} not found${NC}"
        return 1
    fi
    
    # Deploy workflow
    RESPONSE=$(curl -s -b cookies.txt \
        -X POST "${N8N_URL}/rest/workflows" \
        -H "Content-Type: application/json" \
        -H "Authorization: Basic $(echo -n "${N8N_USER}:${N8N_PASSWORD}" | base64)" \
        -d @"${workflow_file}" \
        2>/dev/null || echo "")
    
    if echo "$RESPONSE" | grep -q '"id"'; then
        WORKFLOW_ID=$(echo "$RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        echo -e "${GREEN}✅ ${workflow_name} deployed successfully (ID: ${WORKFLOW_ID})${NC}"
        
        # Activate the workflow
        curl -s -b cookies.txt \
            -X POST "${N8N_URL}/rest/workflows/${WORKFLOW_ID}/activate" \
            -H "Authorization: Basic $(echo -n "${N8N_USER}:${N8N_PASSWORD}" | base64)" \
            >/dev/null 2>&1
        
        echo -e "${GREEN}✅ Workflow activated${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to deploy ${workflow_name}${NC}"
        echo "Response: $RESPONSE"
        return 1
    fi
}

# Function to check environment variables
check_env_vars() {
    echo -e "${YELLOW}Checking environment variables...${NC}"
    
    local missing_vars=()
    
    # Check if .env file exists
    if [ ! -f .env ]; then
        echo -e "${YELLOW}⚠️  .env file not found. Creating template...${NC}"
        cat > .env << EOF
# Notion Configuration
NOTION_API_TOKEN=your_notion_integration_token
NOTION_INBOX_PAGE_ID=your_inbox_page_id
NOTION_CATEGORIES_DB_ID=your_categories_database_id

# Slack Configuration  
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your_slack_signing_secret
EOF
        echo -e "${YELLOW}📝 Please edit .env file with your actual credentials${NC}"
    fi
    
    # Source .env file
    if [ -f .env ]; then
        source .env
    fi
    
    # Check required variables
    [ -z "$NOTION_API_TOKEN" ] && missing_vars+=("NOTION_API_TOKEN")
    [ -z "$NOTION_INBOX_PAGE_ID" ] && missing_vars+=("NOTION_INBOX_PAGE_ID")
    [ -z "$NOTION_CATEGORIES_DB_ID" ] && missing_vars+=("NOTION_CATEGORIES_DB_ID")
    [ -z "$SLACK_BOT_TOKEN" ] && missing_vars+=("SLACK_BOT_TOKEN")
    [ -z "$SLACK_SIGNING_SECRET" ] && missing_vars+=("SLACK_SIGNING_SECRET")
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        echo -e "${YELLOW}⚠️  Missing environment variables:${NC}"
        for var in "${missing_vars[@]}"; do
            echo -e "   - ${var}"
        done
        echo -e "${YELLOW}Please configure these in your .env file before proceeding.${NC}"
        return 1
    else
        echo -e "${GREEN}✅ All required environment variables are set${NC}"
        return 0
    fi
}

# Function to show webhook URL
show_webhook_info() {
    echo -e "${BLUE}📡 Webhook Information${NC}"
    echo "====================="
    echo -e "Webhook URL: ${GREEN}${N8N_URL}/webhook/slack-to-notion${NC}"
    echo -e "HTTP Method: ${GREEN}POST${NC}"
    echo ""
    echo -e "${YELLOW}Configure this URL in your Slack app's Event Subscriptions.${NC}"
}

# Function to show next steps
show_next_steps() {
    echo -e "${BLUE}🎯 Next Steps${NC}"
    echo "============="
    echo "1. Configure your Slack app with the webhook URL above"
    echo "2. Set up Notion integration and get required IDs"
    echo "3. Update your .env file with actual credentials"
    echo "4. Test the integration by sending a message in Slack"
    echo ""
    echo -e "${GREEN}📖 See ALSA_SETUP_GUIDE.md for detailed instructions${NC}"
}

# Main execution
main() {
    echo -e "${BLUE}Starting ALSA deployment...${NC}"
    echo ""
    
    # Check if n8n is running
    if ! check_n8n; then
        echo -e "${RED}Please start n8n first: docker-compose --profile cpu up -d${NC}"
        exit 1
    fi
    
    # Check environment variables
    check_env_vars
    ENV_CHECK=$?
    
    # Get authentication
    get_auth_cookie
    
    # Deploy workflows
    echo ""
    echo -e "${BLUE}Deploying workflows...${NC}"
    
    # Deploy simple workflow first for testing
    if [ -f "alsa-workflow-simple.json" ]; then
        deploy_workflow "alsa-workflow-simple.json" "ALSA Simple Test Workflow"
    fi
    
    # Deploy full ALSA workflow
    if [ -f "alsa-slack-notion-workflow.json" ]; then
        deploy_workflow "alsa-slack-notion-workflow.json" "ALSA Full Workflow"
    fi
    
    # Clean up
    rm -f cookies.txt
    
    echo ""
    show_webhook_info
    echo ""
    show_next_steps
    
    if [ $ENV_CHECK -eq 0 ]; then
        echo -e "${GREEN}🎉 ALSA deployment completed successfully!${NC}"
    else
        echo -e "${YELLOW}⚠️  ALSA workflows deployed, but please configure environment variables.${NC}"
    fi
}

# Run main function
main "$@"
