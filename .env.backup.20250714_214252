POSTGRES_USER=root
POSTGRES_PASSWORD=10RelationalKey
POSTGRES_DB=n8n

N8N_ENCRYPTION_KEY=s5ff1411f92f61b7c90ad05753d8648e1ed001779ef4090a2bdf02f58746b388f
N8N_USER_MANAGEMENT_JWT_SECRET=cf12af86a2905a88c3f0562affc7d06a799ac7fba38c9eae800867dadda6d6ee
N8N_DEFAULT_BINARY_DATA_MODE=filesystem

# For Mac users running OLLAMA locally
# See https://github.com/n8n-io/self-hosted-ai-starter-kit?tab=readme-ov-file#for-mac--apple-silicon-users
OLLAMA_HOST=host.docker.internal:11434

# HTTPS Configuration for Slack OAuth
# Option 1: Use ngrok URL (for development/testing) - ACTIVE
N8N_PROTOCOL=https
WEBHOOK_URL=https://fce761a72592.ngrok-free.app/
N8N_HOST=fce761a72592.ngrok-free.app

# Option 2: Use your domain with SSL (for production)
# N8N_PROTOCOL=https
# WEBHOOK_URL=https://yourdomain.com/
# N8N_HOST=yourdomain.com


