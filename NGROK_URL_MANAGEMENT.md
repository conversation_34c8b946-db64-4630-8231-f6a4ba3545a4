# ngrok URL Management Guide

## 🚨 The Problem: Changing URLs

With the **free ngrok plan**, every time you restart ngrok, you get a **new random URL**:

- ❌ Old URL: `https://fce761a72592.ngrok-free.app` (becomes invalid)
- ✅ New URL: `https://abc123.ngrok-free.app` (new random URL)

This means you need to update:
1. Your Slack app OAuth redirect URL
2. Your n8n environment configuration
3. Any saved bookmarks or references

## 🔧 Solutions

### Option 1: Automated Restart Script (Recommended)

Use the automated script that handles everything:

```bash
# When you need to restart ngrok + n8n
./restart-ngrok-setup.sh
```

**What it does:**
- ✅ Stops old ngrok process
- ✅ Starts new ngrok tunnel
- ✅ Gets the new URL automatically
- ✅ Updates .env file with new URL
- ✅ Restarts n8n with new configuration
- ✅ Opens browser windows for easy access
- ✅ Shows you exactly what to update in Slack

### Option 2: Manual Process

If you prefer manual control:

#### Step 1: Restart ngrok
```bash
# Stop ngrok
pkill ngrok

# Start new tunnel
ngrok http 5678
```

#### Step 2: Get the new URL
```bash
# Get new URL from ngrok API
curl -s http://localhost:4040/api/tunnels | grep -o 'https://[^"]*\.ngrok-free\.app'
```

#### Step 3: Update .env file
```bash
# Edit .env with new URL (replace with your actual URL)
N8N_PROTOCOL=https
WEBHOOK_URL=https://your-new-url.ngrok-free.app/
N8N_HOST=your-new-url.ngrok-free.app
```

#### Step 4: Restart n8n
```bash
docker-compose restart n8n
```

#### Step 5: Update Slack app
- Go to https://api.slack.com/apps
- Update OAuth redirect URL to: `https://your-new-url.ngrok-free.app/rest/oauth2-credential/callback`

## 🛠️ Helpful Scripts

### Quick Status Check
```bash
./verify-oauth-setup.sh
```
- Automatically detects current ngrok URL
- Tests all endpoints
- Shows what needs to be updated

### Test Current Setup
```bash
./test-ngrok-setup.sh
```
- Comprehensive testing of the current setup
- Automatically uses current ngrok URL

## 📋 Workflow for Development

### Daily Development Routine:

1. **Start your development session:**
   ```bash
   # Start all services
   docker-compose --profile cpu up -d
   
   # Start ngrok (if not already running)
   ngrok http 5678
   ```

2. **If you need to restart ngrok:**
   ```bash
   ./restart-ngrok-setup.sh
   ```

3. **Update Slack app settings** with the new OAuth URL shown by the script

4. **Continue development** with the new URLs

### End of Development Session:
```bash
# Stop all services
docker-compose down

# ngrok will stop automatically when you close the terminal
```

## 🔄 Common Scenarios

### Scenario 1: Computer Restart
After restarting your computer:
```bash
cd /path/to/self-hosted-ai-starter-kit
docker-compose --profile cpu up -d
./restart-ngrok-setup.sh
# Update Slack app with new OAuth URL
```

### Scenario 2: ngrok Crashed
If ngrok stops working:
```bash
./restart-ngrok-setup.sh
# Update Slack app with new OAuth URL
```

### Scenario 3: Need Fresh Start
For a completely fresh start:
```bash
docker-compose down
./restart-ngrok-setup.sh
# Update Slack app with new OAuth URL
```

## ⚠️ Important Notes

### Free Plan Limitations:
- ❌ **URL changes** every restart
- ❌ **Session timeout** after 2 hours of inactivity
- ❌ **Limited connections** (40 connections/minute)
- ✅ **No cost** for basic usage

### What to Update Each Time:
1. **Slack App OAuth Settings** (most important)
2. **Any saved bookmarks**
3. **Documentation with hardcoded URLs**
4. **Team members** who need the new URL

### Automation Tips:
- Use the `restart-ngrok-setup.sh` script for consistency
- Keep the ngrok dashboard open: http://localhost:4040
- Bookmark the Slack apps page: https://api.slack.com/apps

## 🎯 Best Practices

1. **Always use the restart script** for consistency
2. **Update Slack immediately** after getting new URL
3. **Test the OAuth flow** before deploying workflows
4. **Keep ngrok running** during development sessions
5. **Document the current URL** for team members

## 🚀 Next Steps

After each restart:
1. ✅ Run `./restart-ngrok-setup.sh`
2. ✅ Update Slack app OAuth settings
3. ✅ Test OAuth flow in n8n
4. ✅ Deploy/test your ALSA workflows

---

**💡 Pro Tip:** Consider upgrading to ngrok Pro ($8/month) for a stable custom domain if you're doing serious development work!
