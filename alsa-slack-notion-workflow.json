{"name": "ALSA Slack-to-Notion Automation", "nodes": [{"id": "webhook-trigger", "name": "Slack Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [100, 200], "parameters": {"path": "slack-to-notion", "httpMethod": "POST", "responseMode": "lastNode", "responseData": "firstEntryJson"}, "onError": "continueRegularOutput", "alwaysOutputData": true}, {"id": "extract-slack-data", "name": "Extract Slack Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [300, 200], "parameters": {"language": "javaScript", "jsCode": "// Extract and validate Slack message data\nconst slackData = items[0].json;\n\n// Extract message text and metadata\nconst messageText = slackData.text || slackData.event?.text || '';\nconst userId = slackData.user_id || slackData.event?.user || '';\nconst channelId = slackData.channel_id || slackData.event?.channel || '';\nconst timestamp = slackData.ts || slackData.event?.ts || Date.now();\nconst threadTs = slackData.thread_ts || slackData.event?.thread_ts || null;\n\n// Validate required data\nif (!messageText || messageText.trim().length === 0) {\n  throw new Error('No message text found in Slack payload');\n}\n\nreturn [{\n  json: {\n    rawPageContent: messageText.trim(),\n    slackUserId: userId,\n    slackChannelId: channelId,\n    slackTimestamp: timestamp,\n    slackThreadTs: threadTs,\n    originalPayload: slackData\n  }\n}];"}}, {"id": "create-temp-notion-page", "name": "Create Temporary Notion Page", "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [500, 200], "parameters": {"resource": "page", "operation": "create", "pageId": "{{ $env.NOTION_INBOX_PAGE_ID }}", "title": "[TEMP] Processing...", "blockUi": [{"type": "paragraph", "text": "{{ $json.rawPageContent }}"}]}}, {"id": "get-parent-categories", "name": "Get Existing Parent Categories", "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [700, 200], "parameters": {"resource": "databasePage", "operation": "getAll", "databaseId": "{{ $env.NOTION_CATEGORIES_DB_ID }}", "limit": 100, "sorts": [{"property": "Name", "direction": "ascending"}]}}, {"id": "alsa-processing-engine", "name": "ALSA Processing Engine", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 200], "parameters": {"language": "javaScript", "jsCode": "// ALSA (Automated Learning & Synthesis Architect) Processing Engine\n// Implements the deterministic logic from the ALSA rules\n\nconst slackData = $node['extract-slack-data'].json;\nconst notionPageData = $node['create-temp-notion-page'].json;\nconst parentCategories = $node['get-parent-categories'].json;\n\n// Input data\nconst triggeringPageId = notionPageData.id;\nconst rawPageContent = slackData.rawPageContent;\nconst potentialParentsList = parentCategories.map(cat => ({\n  pageId: cat.id,\n  title: cat.properties?.Name?.title?.[0]?.plain_text || 'Untitled'\n}));\nconst notionPageUrl = notionPageData.url;\n\n// STEP 1: Triage & Validation\nfunction triageContent(content) {\n  const words = content.trim().split(/\\s+/).filter(word => word.length > 0);\n  const meaningfulWords = words.filter(word => \n    word.length > 2 && \n    !/^(the|and|but|for|are|was|were|been|have|has|had|will|would|could|should|may|might|can|is|am|are|a|an|this|that|these|those)$/i.test(word)\n  );\n  \n  return {\n    isValid: meaningfulWords.length >= 15 && content.length > 20,\n    wordCount: meaningfulWords.length,\n    totalLength: content.length\n  };\n}\n\nfunction detectLanguage(content) {\n  // Simple language detection - check for Chinese characters\n  const chineseRegex = /[\\u4e00-\\u9fff]/;\n  return chineseRegex.test(content) ? 'Simplified Chinese' : 'English';\n}\n\nconst contentAnalysis = triageContent(rawPageContent);\nlet decision, detectedLanguage = null;\n\nif (!contentAnalysis.isValid) {\n  decision = 'insufficient_content';\n} else {\n  detectedLanguage = detectLanguage(rawPageContent);\n  \n  // STEP 2: Content Synthesis\n  const newTitle = generateTitle(rawPageContent, detectedLanguage);\n  const summaryMarkdown = generateSummary(rawPageContent, detectedLanguage);\n  \n  // STEP 3: Hierarchical Classification\n  const parentMatch = findBestParentMatch(rawPageContent, potentialParentsList, detectedLanguage);\n  \n  if (parentMatch.confidence > 0.95 && parentMatch.parent) {\n    decision = 'use_existing_parent';\n  } else {\n    decision = 'create_new_parent';\n  }\n}\n\n// Helper functions\nfunction generateTitle(content, language) {\n  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);\n  const firstSentence = sentences[0]?.trim() || content.substring(0, 100);\n  \n  // Extract key concepts and create descriptive title\n  const words = firstSentence.split(/\\s+/).slice(0, 8);\n  let title = words.join(' ');\n  \n  if (title.length > 60) {\n    title = title.substring(0, 57) + '...';\n  }\n  \n  return title || 'Untitled Note';\n}\n\nfunction generateSummary(content, language) {\n  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 10);\n  const summary = sentences.slice(0, 3).join('. ');\n  \n  if (language === 'Simplified Chinese') {\n    return `**摘要：** ${summary}\\n\\n这是从Slack消息自动创建的笔记。`;\n  } else {\n    return `**Summary:** ${summary}\\n\\nThis note was automatically created from a Slack message.`;\n  }\n}\n\nfunction findBestParentMatch(content, parents, language) {\n  if (!parents || parents.length === 0) {\n    return { confidence: 0, parent: null };\n  }\n  \n  const contentLower = content.toLowerCase();\n  let bestMatch = { confidence: 0, parent: null };\n  \n  for (const parent of parents) {\n    const titleLower = parent.title.toLowerCase();\n    const titleWords = titleLower.split(/\\s+/);\n    \n    let matchScore = 0;\n    for (const word of titleWords) {\n      if (word.length > 3 && contentLower.includes(word)) {\n        matchScore += 0.3;\n      }\n    }\n    \n    // Boost score for exact phrase matches\n    if (contentLower.includes(titleLower)) {\n      matchScore += 0.5;\n    }\n    \n    if (matchScore > bestMatch.confidence) {\n      bestMatch = { confidence: matchScore, parent };\n    }\n  }\n  \n  return bestMatch;\n}\n\nfunction generateNewParentTitle(content, language) {\n  // Extract domain/topic from content\n  const topics = {\n    'api': 'API Development',\n    'database': 'Database Management', \n    'auth': 'Authentication & Security',\n    'deploy': 'Deployment & DevOps',\n    'bug': 'Bug Reports & Issues',\n    'feature': 'Feature Requests',\n    'meeting': 'Meeting Notes',\n    'idea': 'Ideas & Brainstorming'\n  };\n  \n  const contentLower = content.toLowerCase();\n  for (const [keyword, category] of Object.entries(topics)) {\n    if (contentLower.includes(keyword)) {\n      return language === 'Simplified Chinese' ? \n        `${category} (中文)` : category;\n    }\n  }\n  \n  return language === 'Simplified Chinese' ? '一般笔记' : 'General Notes';\n}\n\nfunction generateSlackMessage(decision, data, language) {\n  const lang = language || 'English';\n  \n  if (decision === 'insufficient_content') {\n    return lang === 'Simplified Chinese' ?\n      `⚠️ 您的笔记内容太短或不够清晰，无法处理。已标记为需要人工审核：${data.notionPageUrl}` :\n      `⚠️ Your note could not be processed because it appears to be too short or is missing a clear topic. It has been flagged for manual review here: ${data.notionPageUrl}`;\n  }\n  \n  if (decision === 'use_existing_parent') {\n    return lang === 'Simplified Chinese' ?\n      `✅ 您的笔记 '${data.newTitle}' 已归类到 **${data.existingParentTitle}**。查看链接：${data.notionPageUrl}` :\n      `✅ Your note '${data.newTitle}' has been categorized under **${data.existingParentTitle}**. View it here: ${data.notionPageUrl}`;\n  }\n  \n  if (decision === 'create_new_parent') {\n    return lang === 'Simplified Chinese' ?\n      `✅ 您的笔记 '${data.newTitle}' 已保存，并创建了新分类 **${data.newParentTitle}**。查看链接：${data.notionPageUrl}` :\n      `✅ Your note '${data.newTitle}' has been saved and a new category **${data.newParentTitle}** was created. View it here: ${data.notionPageUrl}`;\n  }\n}\n\n// Generate final output based on decision\nlet result;\n\nif (decision === 'insufficient_content') {\n  result = {\n    processingMetadata: {\n      decision: 'insufficient_content',\n      detectedLanguage: null\n    },\n    childPageUpdate: {\n      pageId: triggeringPageId,\n      newTitle: '[FLAGGED] Insufficient Content'\n    },\n    parentInfo: {\n      existingParentId: null,\n      existingParentTitle: null,\n      newParentTitle: null\n    },\n    syncedContent: {\n      toggleTitle: null,\n      summaryMarkdown: null\n    },\n    notificationPayload: {\n      slackReplyMessage: generateSlackMessage(decision, { notionPageUrl }, null)\n    }\n  };\n} else {\n  const newTitle = generateTitle(rawPageContent, detectedLanguage);\n  const summaryMarkdown = generateSummary(rawPageContent, detectedLanguage);\n  \n  if (decision === 'use_existing_parent') {\n    const parentMatch = findBestParentMatch(rawPageContent, potentialParentsList, detectedLanguage);\n    \n    result = {\n      processingMetadata: {\n        decision: 'use_existing_parent',\n        detectedLanguage\n      },\n      childPageUpdate: {\n        pageId: triggeringPageId,\n        newTitle\n      },\n      parentInfo: {\n        existingParentId: parentMatch.parent.pageId,\n        existingParentTitle: parentMatch.parent.title,\n        newParentTitle: null\n      },\n      syncedContent: {\n        toggleTitle: newTitle,\n        summaryMarkdown\n      },\n      notificationPayload: {\n        slackReplyMessage: generateSlackMessage(decision, {\n          newTitle,\n          existingParentTitle: parentMatch.parent.title,\n          notionPageUrl\n        }, detectedLanguage)\n      }\n    };\n  } else {\n    const newParentTitle = generateNewParentTitle(rawPageContent, detectedLanguage);\n    \n    result = {\n      processingMetadata: {\n        decision: 'create_new_parent',\n        detectedLanguage\n      },\n      childPageUpdate: {\n        pageId: triggeringPageId,\n        newTitle\n      },\n      parentInfo: {\n        existingParentId: null,\n        existingParentTitle: null,\n        newParentTitle\n      },\n      syncedContent: {\n        toggleTitle: newTitle,\n        summaryMarkdown\n      },\n      notificationPayload: {\n        slackReplyMessage: generateSlackMessage(decision, {\n          newTitle,\n          newParentTitle,\n          notionPageUrl\n        }, detectedLanguage)\n      }\n    };\n  }\n}\n\nreturn [{ json: result }];"}}, {"id": "decision-router", "name": "Decision Router", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1100, 200], "parameters": {"rules": {"rules": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.processingMetadata.decision }}", "rightValue": "insufficient_content", "operator": {"type": "string", "operation": "equals"}}]}, "renameOutput": true, "outputKey": "insufficient_content"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.processingMetadata.decision }}", "rightValue": "use_existing_parent", "operator": {"type": "string", "operation": "equals"}}]}, "renameOutput": true, "outputKey": "use_existing_parent"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.processingMetadata.decision }}", "rightValue": "create_new_parent", "operator": {"type": "string", "operation": "equals"}}]}, "renameOutput": true, "outputKey": "create_new_parent"}]}}}, {"id": "flag-content", "name": "Flag Insufficient Content", "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [1300, 100], "parameters": {"resource": "page", "operation": "update", "pageId": "={{ $json.childPageUpdate.pageId }}", "title": "={{ $json.childPageUpdate.newTitle }}", "blockUi": [{"type": "callout", "text": "⚠️ This content was flagged as insufficient for automatic processing. Please review and add more details.", "icon": "⚠️"}]}}, {"id": "move-to-existing-parent", "name": "Move to Existing Parent", "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [1300, 200], "parameters": {"resource": "page", "operation": "update", "pageId": "={{ $json.childPageUpdate.pageId }}", "title": "={{ $json.childPageUpdate.newTitle }}", "parentPageId": "={{ $json.parentInfo.existingParentId }}", "blockUi": [{"type": "toggle", "text": "={{ $json.syncedContent.toggleTitle }}", "children": [{"type": "paragraph", "text": "={{ $json.syncedContent.summaryMarkdown }}"}]}]}}, {"id": "create-new-parent", "name": "Create New Parent Category", "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [1300, 300], "parameters": {"resource": "databasePage", "operation": "create", "databaseId": "{{ $env.NOTION_CATEGORIES_DB_ID }}", "title": "={{ $json.parentInfo.newParentTitle }}", "propertiesUi": {"propertyValues": [{"key": "Name", "textValue": "={{ $json.parentInfo.newParentTitle }}"}, {"key": "Created", "dateValue": "={{ $now }}"}]}}}, {"id": "move-page-to-new-parent", "name": "Move Page to New Parent", "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [1500, 300], "parameters": {"resource": "page", "operation": "update", "pageId": "={{ $node['alsa-processing-engine'].json.childPageUpdate.pageId }}", "title": "={{ $node['alsa-processing-engine'].json.childPageUpdate.newTitle }}", "parentPageId": "={{ $json.id }}", "blockUi": [{"type": "toggle", "text": "={{ $node['alsa-processing-engine'].json.syncedContent.toggleTitle }}", "children": [{"type": "paragraph", "text": "={{ $node['alsa-processing-engine'].json.syncedContent.summaryMarkdown }}"}]}]}}, {"id": "send-flag-notification", "name": "Send Flag Notification", "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1500, 100], "parameters": {"resource": "message", "operation": "post", "channel": "={{ $node['extract-slack-data'].json.slackChannelId }}", "text": "={{ $node['alsa-processing-engine'].json.notificationPayload.slackReplyMessage }}", "otherOptions": {"thread_ts": "={{ $node['extract-slack-data'].json.slackThreadTs }}"}}}, {"id": "send-success-notification", "name": "Send Success Notification", "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1700, 200], "parameters": {"resource": "message", "operation": "post", "channel": "={{ $node['extract-slack-data'].json.slackChannelId }}", "text": "={{ $node['alsa-processing-engine'].json.notificationPayload.slackReplyMessage }}", "otherOptions": {"thread_ts": "={{ $node['extract-slack-data'].json.slackThreadTs }}"}}}, {"id": "send-new-parent-notification", "name": "Send New Parent Notification", "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1700, 300], "parameters": {"resource": "message", "operation": "post", "channel": "={{ $node['extract-slack-data'].json.slackChannelId }}", "text": "={{ $node['alsa-processing-engine'].json.notificationPayload.slackReplyMessage }}", "otherOptions": {"thread_ts": "={{ $node['extract-slack-data'].json.slackThreadTs }}"}}}], "connections": {"webhook-trigger": {"main": [[{"node": "extract-slack-data", "type": "main", "index": 0}]]}, "extract-slack-data": {"main": [[{"node": "create-temp-notion-page", "type": "main", "index": 0}]]}, "create-temp-notion-page": {"main": [[{"node": "get-parent-categories", "type": "main", "index": 0}]]}, "get-parent-categories": {"main": [[{"node": "alsa-processing-engine", "type": "main", "index": 0}]]}, "alsa-processing-engine": {"main": [[{"node": "decision-router", "type": "main", "index": 0}]]}, "decision-router": {"main": [[{"node": "flag-content", "type": "main", "index": 0}], [{"node": "move-to-existing-parent", "type": "main", "index": 0}], [{"node": "create-new-parent", "type": "main", "index": 0}]]}, "flag-content": {"main": [[{"node": "send-flag-notification", "type": "main", "index": 0}]]}, "move-to-existing-parent": {"main": [[{"node": "send-success-notification", "type": "main", "index": 0}]]}, "create-new-parent": {"main": [[{"node": "move-page-to-new-parent", "type": "main", "index": 0}]]}, "move-page-to-new-parent": {"main": [[{"node": "send-new-parent-notification", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "saveManualExecutions": true}}