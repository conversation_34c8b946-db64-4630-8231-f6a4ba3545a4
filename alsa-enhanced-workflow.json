{"name": "ALSA Enhanced - AI-Powered Slack-to-Notion", "nodes": [{"id": "webhook-trigger", "name": "Slack Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [100, 300], "parameters": {"path": "alsa-enhanced", "httpMethod": "POST", "responseMode": "lastNode", "responseData": "firstEntryJson"}, "onError": "continueRegularOutput", "alwaysOutputData": true}, {"id": "extract-slack-data", "name": "Extract & Validate Slack Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [300, 300], "parameters": {"language": "javaScript", "jsCode": "// Enhanced Slack data extraction with metadata\nconst slackData = items[0].json;\n\n// Extract comprehensive message data\nconst messageText = slackData.text || slackData.event?.text || '';\nconst userId = slackData.user_id || slackData.event?.user || '';\nconst channelId = slackData.channel_id || slackData.event?.channel || '';\nconst timestamp = slackData.ts || slackData.event?.ts || Date.now();\nconst threadTs = slackData.thread_ts || slackData.event?.thread_ts || null;\nconst messageType = slackData.event?.subtype || 'message';\nconst isBot = slackData.event?.bot_id ? true : false;\nconst hasAttachments = slackData.event?.files?.length > 0;\nconst mentionsCount = (messageText.match(/<@[UW][A-Z0-9]+>/g) || []).length;\nconst hasUrls = /https?:\\/\\/[^\\s]+/.test(messageText);\n\n// Enhanced validation\nif (!messageText || messageText.trim().length === 0) {\n  throw new Error('No message text found in Slack payload');\n}\n\n// Skip bot messages unless explicitly configured\nif (isBot && !process.env.ALSA_PROCESS_BOT_MESSAGES) {\n  throw new Error('Bot message skipped');\n}\n\n// Extract urgency indicators\nconst urgencyKeywords = ['urgent', 'asap', 'emergency', 'critical', 'immediate', '🚨', '⚠️'];\nconst isUrgent = urgencyKeywords.some(keyword => \n  messageText.toLowerCase().includes(keyword.toLowerCase())\n);\n\n// Extract action indicators\nconst actionKeywords = ['todo', 'task', 'action', 'follow up', 'reminder', '✅', '📝'];\nconst isActionItem = actionKeywords.some(keyword => \n  messageText.toLowerCase().includes(keyword.toLowerCase())\n);\n\nreturn [{\n  json: {\n    rawPageContent: messageText.trim(),\n    slackUserId: userId,\n    slackChannelId: channelId,\n    slackTimestamp: timestamp,\n    slackThreadTs: threadTs,\n    messageMetadata: {\n      type: messageType,\n      isBot: isBot,\n      hasAttachments: hasAttachments,\n      mentionsCount: mentionsCount,\n      hasUrls: hasUrls,\n      isUrgent: isUrgent,\n      isActionItem: isActionItem,\n      wordCount: messageText.trim().split(/\\s+/).length,\n      characterCount: messageText.length\n    },\n    originalPayload: slackData\n  }\n}];"}}, {"id": "ai-content-analysis", "name": "AI Content Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 300], "parameters": {"url": "http://ollama:11434/api/generate", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "llama3"}, {"name": "prompt", "value": "Analyze this message and provide a JSON response with sentiment (positive/negative/neutral), priority (high/medium/low), main topics (array), and suggested tags (array). Message: {{ $json.rawPageContent }}"}, {"name": "stream", "value": false}, {"name": "format", "value": "json"}]}, "options": {"timeout": 30000}}, "onError": "continueRegularOutput"}, {"id": "process-ai-analysis", "name": "Process AI Analysis", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, 300], "parameters": {"language": "javaScript", "jsCode": "// Process AI analysis results\nconst slackData = $node['Extract & Validate Slack Data'].json;\nconst aiResponse = items[0].json;\n\nlet aiAnalysis = {\n  sentiment: 'neutral',\n  priority: 'medium',\n  topics: [],\n  suggestedTags: []\n};\n\n// Parse AI response safely\ntry {\n  if (aiResponse.response) {\n    const parsed = JSON.parse(aiResponse.response);\n    aiAnalysis = {\n      sentiment: parsed.sentiment || 'neutral',\n      priority: parsed.priority || 'medium',\n      topics: Array.isArray(parsed.topics) ? parsed.topics : [],\n      suggestedTags: Array.isArray(parsed.suggested_tags) ? parsed.suggested_tags : []\n    };\n  }\n} catch (error) {\n  console.log('AI analysis parsing failed, using defaults:', error.message);\n}\n\n// Combine with metadata analysis\nconst enhancedData = {\n  ...slackData,\n  aiAnalysis: aiAnalysis,\n  enhancedMetadata: {\n    ...slackData.messageMetadata,\n    finalPriority: slackData.messageMetadata.isUrgent ? 'high' : aiAnalysis.priority,\n    combinedTags: [\n      ...aiAnalysis.suggestedTags,\n      ...(slackData.messageMetadata.isActionItem ? ['action-item'] : []),\n      ...(slackData.messageMetadata.isUrgent ? ['urgent'] : []),\n      ...(slackData.messageMetadata.hasUrls ? ['contains-links'] : []),\n      ...(slackData.messageMetadata.hasAttachments ? ['has-attachments'] : [])\n    ].filter((tag, index, arr) => arr.indexOf(tag) === index) // Remove duplicates\n  }\n};\n\nreturn [{ json: enhancedData }];"}}, {"id": "create-enhanced-notion-page", "name": "Create Enhanced Notion Page", "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [900, 300], "parameters": {"resource": "page", "operation": "create", "pageId": "{{ $env.NOTION_INBOX_PAGE_ID }}", "title": "[{{ $json.enhancedMetadata.finalPriority.toUpperCase() }}] Processing...", "blockUi": [{"type": "callout", "text": "🤖 AI Analysis: {{ $json.aiAnalysis.sentiment }} sentiment, {{ $json.aiAnalysis.priority }} priority", "icon": "🤖"}, {"type": "paragraph", "text": "{{ $json.rawPageContent }}"}, {"type": "divider"}, {"type": "paragraph", "text": "**Metadata:** {{ $json.enhancedMetadata.wordCount }} words, {{ $json.enhancedMetadata.mentionsCount }} mentions"}]}}], "connections": {"Slack Webhook": {"main": [[{"node": "Extract & Validate Slack Data", "type": "main", "index": 0}]]}, "Extract & Validate Slack Data": {"main": [[{"node": "AI Content Analysis", "type": "main", "index": 0}]]}, "AI Content Analysis": {"main": [[{"node": "Process AI Analysis", "type": "main", "index": 0}]]}, "Process AI Analysis": {"main": [[{"node": "Create Enhanced Notion Page", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "saveManualExecutions": true, "timezone": "Asia/Hong_Kong"}}