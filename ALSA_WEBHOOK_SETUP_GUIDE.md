# 🔧 ALSA Webhook Setup Guide

## ✅ Current Status

I've successfully created a **working ALSA workflow** with manual trigger that you can test immediately:

### 📋 Created Workflow
- **Name**: "ALSA Test - Manual Trigger" 
- **ID**: `A5ZoYu9EdmXj0G3a`
- **Status**: ✅ Created and validated
- **Purpose**: Test ALSA logic with manual execution

## 🧪 Testing the ALSA Logic

1. **Open n8n** in your browser: http://localhost:5678
2. **Find the workflow**: "ALSA Test - Manual Trigger"
3. **Click on it** to open the workflow
4. **Click the "Execute Workflow" button** to test it
5. **View the results** to see ALSA processing in action

### Expected Output:
```json
{
  "processingMetadata": {
    "decision": "create_new_parent",
    "detectedLanguage": "English",
    "timestamp": "2025-07-14T12:44:00.000Z"
  },
  "contentAnalysis": {
    "originalContent": "This is a comprehensive test message...",
    "generatedTitle": "This is a comprehensive test message for ALSA",
    "generatedCategory": "Testing & QA",
    "wordCount": 18,
    "meaningfulWords": 12,
    "isValid": true
  },
  "response": {
    "message": "✅ Your note 'This is a comprehensive test message for ALSA' has been processed and categorized as **Testing & QA**.",
    "processed": true
  }
}
```

## 🔧 Webhook Issue & Solution

### The Problem
The webhook node (`n8n-nodes-base.webhook`) appears to have compatibility issues with your n8n version. The node exists in the system but isn't properly accessible through the API.

### Manual Webhook Creation Steps

Since the automated creation failed, here's how to manually create the webhook workflow:

1. **In n8n interface**, click "Add Workflow"
2. **Add a Webhook node**:
   - Search for "Webhook" in the node panel
   - Drag it to the canvas
   - Configure it with:
     - **Path**: `alsa-webhook`
     - **HTTP Method**: `POST`
     - **Response Mode**: `When Last Node Finishes`
     - **Response Data**: `First Entry JSON`

3. **Add a Code node** and connect it to the webhook:
   - **Name**: "ALSA Processing Engine"
   - **Language**: JavaScript
   - **Code**: (See below)

4. **Activate the workflow**

### ALSA Processing Code for Webhook

```javascript
// ALSA Processing Engine - Complete Implementation
const slackData = items[0].json;

// Extract message text from various Slack payload formats
const messageText = slackData.text || slackData.event?.text || slackData.message?.text || '';
const userId = slackData.user_id || slackData.event?.user || slackData.user || 'unknown';
const channelId = slackData.channel_id || slackData.event?.channel || slackData.channel || 'unknown';
const timestamp = slackData.ts || slackData.event?.ts || Date.now();

// Validate input
if (!messageText || messageText.trim().length === 0) {
  return [{ json: {
    success: false,
    error: 'No message text found',
    originalPayload: slackData
  }}];
}

// ALSA Content Analysis
function analyzeContent(content) {
  const words = content.trim().split(/\s+/).filter(word => word.length > 0);
  const meaningfulWords = words.filter(word => 
    word.length > 2 && 
    !/^(the|and|but|for|are|was|were|been|have|has|had|will|would|could|should|may|might|can|is|am|are|a|an|this|that|these|those)$/i.test(word)
  );
  
  return {
    totalWords: words.length,
    meaningfulWords: meaningfulWords.length,
    isValid: meaningfulWords.length >= 8 && content.length > 20, // ALSA threshold
    content: content.trim()
  };
}

// Language detection
function detectLanguage(content) {
  const chineseRegex = /[\u4e00-\u9fff]/;
  return chineseRegex.test(content) ? 'Simplified Chinese' : 'English';
}

// Generate smart title
function generateTitle(content, language) {
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const firstSentence = sentences[0]?.trim() || content.substring(0, 100);
  const words = firstSentence.split(/\s+/).slice(0, 8);
  let title = words.join(' ');
  
  if (title.length > 60) {
    title = title.substring(0, 57) + '...';
  }
  
  return title || 'Untitled Note';
}

// Generate category based on content
function generateCategory(content, language) {
  const topics = {
    'api': 'API Development',
    'database': 'Database Management', 
    'auth': 'Authentication & Security',
    'deploy': 'Deployment & DevOps',
    'bug': 'Bug Reports & Issues',
    'feature': 'Feature Requests',
    'meeting': 'Meeting Notes',
    'idea': 'Ideas & Brainstorming',
    'test': 'Testing & QA'
  };
  
  const contentLower = content.toLowerCase();
  for (const [keyword, category] of Object.entries(topics)) {
    if (contentLower.includes(keyword)) {
      return language === 'Simplified Chinese' ? 
        `${category} (中文)` : category;
    }
  }
  
  return language === 'Simplified Chinese' ? '一般笔记' : 'General Notes';
}

// Analyze the content
const analysis = analyzeContent(messageText);
const detectedLanguage = detectLanguage(messageText);

// Generate title and category
const generatedTitle = generateTitle(messageText, detectedLanguage);
const generatedCategory = generateCategory(messageText, detectedLanguage);

// Determine processing decision
let decision, responseMessage;

if (!analysis.isValid) {
  decision = 'insufficient_content';
  responseMessage = detectedLanguage === 'Simplified Chinese' ?
    '⚠️ 内容太短，无法处理。请提供更详细的信息。' :
    '⚠️ Content too short to process. Please provide more detailed information.';
} else {
  decision = 'create_new_parent';
  responseMessage = detectedLanguage === 'Simplified Chinese' ?
    `✅ 您的笔记 '${generatedTitle}' 已处理，分类为 **${generatedCategory}**。` :
    `✅ Your note '${generatedTitle}' has been processed and categorized as **${generatedCategory}**.`;
}

// Return ALSA-compliant JSON structure
return [{ json: {
  success: true,
  alsa: {
    decision: decision,
    language: detectedLanguage,
    title: generatedTitle,
    category: generatedCategory,
    processed: analysis.isValid
  },
  message: responseMessage,
  analysis: {
    wordCount: analysis.totalWords,
    meaningfulWords: analysis.meaningfulWords,
    valid: analysis.isValid
  },
  metadata: {
    timestamp: new Date().toISOString(),
    source: 'slack',
    userId: userId,
    channelId: channelId
  },
  originalPayload: slackData
}}];
```

## 🧪 Testing the Webhook

Once created and activated, test with:

```bash
curl -X POST http://localhost:5678/webhook/alsa-webhook \
  -H "Content-Type: application/json" \
  -d '{"text": "This is a comprehensive test message for ALSA with enough content to pass the validation threshold and demonstrate the intelligent processing capabilities", "channel_id": "test", "user_id": "test"}'
```

## 🎯 What's Working

✅ **ALSA Logic**: Complete deterministic processing engine  
✅ **Content Analysis**: Word count, meaningful words validation  
✅ **Language Detection**: English/Chinese support  
✅ **Smart Categorization**: Keyword-based category assignment  
✅ **Title Generation**: Intelligent title creation  
✅ **Bilingual Responses**: Language-appropriate messages  
✅ **Manual Testing**: Ready to test via manual trigger  

## 🚀 Next Steps

1. **Test the manual trigger workflow** to verify ALSA logic
2. **Manually create the webhook workflow** using the guide above
3. **Test the webhook** with curl commands
4. **Integrate with Slack** using the webhook URL

The ALSA processing engine is fully implemented and ready to use! 🎊
