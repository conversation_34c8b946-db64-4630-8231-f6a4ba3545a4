version: '3.8'

volumes:
  n8n_storage:
  postgres_storage:
  ollama_storage:
  qdrant_storage:

networks:
  demo:

# --- Reusable service definitions (Anchors) ---
x-n8n: &service-n8n
  image: n8nio/n8n:latest
  networks: ['demo']
  environment:
    - DB_TYPE=postgresdb
    - DB_POSTGRESDB_HOST=postgres
    - DB_POSTGRESDB_PORT=5432
    - DB_POSTGRESDB_DATABASE=${POSTGRES_DB}
    - DB_POSTGRESDB_USER=${POSTGRES_USER}
    - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD}
    - N8N_BASIC_AUTH_ACTIVE=true
    - N8N_BASIC_AUTH_USER=Paul
    - N8N_BASIC_AUTH_PASSWORD=Super-safe-password1
    - N8N_HOST=${N8N_HOST:-0.0.0.0}
    - N8N_PORT=5678
    - N8N_PROTOCOL=${N8N_PROTOCOL:-http}
    - WEBHOOK_URL=${WEBHOOK_URL:-http://localhost:5678/}
    - GENERIC_TIMEZONE=Asia/Hong_Kong
    - N8N_DIAGNOSTICS_ENABLED=false
    - N8N_PERSONALIZATION_ENABLED=false
    - OLLAMA_HOST=host.docker.internal:11434
  env_file:
    - .env # This will load N8N_ENCRYPTION_KEY and other secrets

x-ollama: &service-ollama
  image: ollama/ollama:latest
  container_name: ollama
  networks: ['demo']
  restart: unless-stopped
  ports:
    - "11434:11434"
  volumes:
    - ollama_storage:/root/.ollama

x-init-ollama: &init-ollama
  image: ollama/ollama:latest
  networks: ['demo']
  container_name: ollama-pull-model
  volumes:
    - ollama_storage:/root/.ollama
  entrypoint: /bin/sh
  environment:
    - OLLAMA_HOST=ollama:11434
  command:
    - "-c"
    - "sleep 5; ollama pull llama3" # Changed to llama3 for a common default

# --- Main Services ---
services:
  postgres:
    image: postgres:15
    hostname: postgres
    container_name: n8n-postgres
    networks: ['demo']
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_storage:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -h localhost -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # This service is for one-time setup, we can comment it out after the first run if needed.
  # n8n-import:
  #   <<: *service-n8n
  #   hostname: n8n-import
  #   container_name: n8n-import
  #   entrypoint: /bin/sh
  #   command:
  #     - "-c"
  #     - "n8n import:credentials --separate --input=/demo-data/credentials && n8n import:workflow --separate --input=/demo-data/workflows"
  #   volumes:
  #     - ./n8n/demo-data:/demo-data
  #   depends_on:
  #     postgres:
  #       condition: service_healthy

  n8n:
    <<: *service-n8n
    hostname: n8n
    container_name: n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    volumes:
      - n8n_storage:/home/<USER>/.n8n
      # - ./n8n/demo-data:/demo-data # You can uncomment this if you have demo data
      # - ./shared:/data/shared # You can uncomment this for sharing files with the host
    depends_on:
      postgres:
        condition: service_healthy
      # n8n-import: # Uncomment this if you are using the n8n-import service
      #   condition: service_completed_successfully

  qdrant:
    image: qdrant/qdrant
    hostname: qdrant
    container_name: qdrant
    networks: ['demo']
    restart: unless-stopped
    ports:
      - "6333:6333"
    volumes:
      - qdrant_storage:/qdrant/storage

  nginx:
    image: nginx:alpine
    hostname: nginx
    container_name: n8n-nginx
    networks: ['demo']
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl/cert.pem:/etc/ssl/certs/cert.pem:ro
      - ./ssl/key.pem:/etc/ssl/private/key.pem:ro
    depends_on:
      - n8n

  # --- Profile-based Ollama Services ---
  ollama-cpu:
    profiles: ["cpu"]
    <<: *service-ollama

  ollama-gpu:
    profiles: ["gpu"]
    <<: *service-ollama
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  ollama-pull-cpu:
    profiles: ["cpu"]
    <<: *init-ollama
    depends_on:
      - ollama-cpu

  ollama-pull-gpu:
    profiles: ["gpu"]
    <<: *init-ollama
    depends_on:
      - ollama-gpu