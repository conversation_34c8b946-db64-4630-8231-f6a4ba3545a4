{"name": "ALSA Daily Summary & Analytics", "nodes": [{"id": "daily-schedule", "name": "Daily Schedule", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [100, 300], "parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 9 * * *"}]}}}, {"id": "get-yesterdays-pages", "name": "Get Yesterday's Pages", "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [300, 300], "parameters": {"resource": "databasePage", "operation": "getAll", "databaseId": "{{ $env.NOTION_CATEGORIES_DB_ID }}", "filterRequest": {"filters": [{"property": "Created", "date": {"after": "{{ $today.minus({days: 1}).toISODate() }}"}}]}, "limit": 100}}, {"id": "analyze-daily-activity", "name": "Analyze Daily Activity", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [500, 300], "parameters": {"language": "javaScript", "jsCode": "// Analyze yesterday's ALSA activity\nconst pages = items;\nconst yesterday = DateTime.now().minus({days: 1});\n\n// Initialize analytics\nconst analytics = {\n  date: yesterday.toISODate(),\n  totalPages: pages.length,\n  categories: {},\n  priorities: { high: 0, medium: 0, low: 0 },\n  sources: { slack: 0, email: 0, other: 0 },\n  sentiments: { positive: 0, negative: 0, neutral: 0 },\n  topTopics: {},\n  urgentItems: [],\n  actionItems: [],\n  wordCounts: [],\n  hourlyDistribution: Array(24).fill(0)\n};\n\n// Process each page\npages.forEach(page => {\n  const props = page.json.properties || {};\n  \n  // Extract title and content\n  const title = props.Name?.title?.[0]?.plain_text || 'Untitled';\n  const category = props.Category?.select?.name || 'Uncategorized';\n  const priority = props.Priority?.select?.name || 'medium';\n  const source = props.Source?.select?.name || 'unknown';\n  const sentiment = props.Sentiment?.select?.name || 'neutral';\n  const tags = props.Tags?.multi_select?.map(tag => tag.name) || [];\n  const wordCount = props.WordCount?.number || 0;\n  const createdTime = props.Created?.created_time;\n  \n  // Update category counts\n  analytics.categories[category] = (analytics.categories[category] || 0) + 1;\n  \n  // Update priority counts\n  if (priority in analytics.priorities) {\n    analytics.priorities[priority]++;\n  }\n  \n  // Update source counts\n  if (source in analytics.sources) {\n    analytics.sources[source]++;\n  }\n  \n  // Update sentiment counts\n  if (sentiment in analytics.sentiments) {\n    analytics.sentiments[sentiment]++;\n  }\n  \n  // Track topics from tags\n  tags.forEach(tag => {\n    analytics.topTopics[tag] = (analytics.topTopics[tag] || 0) + 1;\n  });\n  \n  // Track urgent and action items\n  if (priority === 'high' || tags.includes('urgent')) {\n    analytics.urgentItems.push({ title, category, tags });\n  }\n  \n  if (tags.includes('action-item')) {\n    analytics.actionItems.push({ title, category, priority });\n  }\n  \n  // Track word counts for average\n  if (wordCount > 0) {\n    analytics.wordCounts.push(wordCount);\n  }\n  \n  // Track hourly distribution\n  if (createdTime) {\n    const hour = new Date(createdTime).getHours();\n    analytics.hourlyDistribution[hour]++;\n  }\n});\n\n// Calculate derived metrics\nanalytics.averageWordCount = analytics.wordCounts.length > 0 \n  ? Math.round(analytics.wordCounts.reduce((a, b) => a + b, 0) / analytics.wordCounts.length)\n  : 0;\n\nanalytics.peakHour = analytics.hourlyDistribution.indexOf(Math.max(...analytics.hourlyDistribution));\n\n// Sort top topics\nanalytics.topTopics = Object.entries(analytics.topTopics)\n  .sort(([,a], [,b]) => b - a)\n  .slice(0, 10)\n  .reduce((obj, [key, value]) => ({ ...obj, [key]: value }), {});\n\n// Generate insights\nconst insights = [];\n\nif (analytics.totalPages === 0) {\n  insights.push('📊 No new content was processed yesterday.');\n} else {\n  insights.push(`📊 Processed ${analytics.totalPages} items yesterday.`);\n  \n  if (analytics.priorities.high > 0) {\n    insights.push(`🚨 ${analytics.priorities.high} high-priority items need attention.`);\n  }\n  \n  if (analytics.actionItems.length > 0) {\n    insights.push(`✅ ${analytics.actionItems.length} action items were identified.`);\n  }\n  \n  const topCategory = Object.entries(analytics.categories)\n    .sort(([,a], [,b]) => b - a)[0];\n  if (topCategory) {\n    insights.push(`📂 Most active category: ${topCategory[0]} (${topCategory[1]} items).`);\n  }\n  \n  if (analytics.peakHour >= 0) {\n    insights.push(`⏰ Peak activity hour: ${analytics.peakHour}:00.`);\n  }\n  \n  const sentimentRatio = analytics.sentiments.positive / (analytics.sentiments.negative || 1);\n  if (sentimentRatio > 2) {\n    insights.push('😊 Overall sentiment was positive yesterday.');\n  } else if (sentimentRatio < 0.5) {\n    insights.push('😟 Overall sentiment was negative yesterday.');\n  }\n}\n\nanalytics.insights = insights;\n\nreturn [{ json: analytics }];"}}, {"id": "generate-ai-summary", "name": "Generate AI Summary", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [700, 300], "parameters": {"url": "http://ollama:11434/api/generate", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "llama3"}, {"name": "prompt", "value": "Based on this ALSA analytics data, write a concise daily summary highlighting key insights, trends, and recommendations for the team. Data: {{ JSON.stringify($json) }}"}, {"name": "stream", "value": false}]}}}, {"id": "create-summary-page", "name": "Create Summary Page", "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [900, 300], "parameters": {"resource": "page", "operation": "create", "pageId": "{{ $env.NOTION_SUMMARIES_PAGE_ID }}", "title": "ALSA Daily Summary - {{ $node['Analyze Daily Activity'].json.date }}", "blockUi": [{"type": "heading_2", "text": "📊 Daily Analytics"}, {"type": "paragraph", "text": "**Total Items Processed:** {{ $node['Analyze Daily Activity'].json.totalPages }}"}, {"type": "paragraph", "text": "**Average Word Count:** {{ $node['Analyze Daily Activity'].json.averageWordCount }}"}, {"type": "paragraph", "text": "**Peak Activity Hour:** {{ $node['Analyze Daily Activity'].json.peakHour }}:00"}, {"type": "heading_3", "text": "🎯 Priority Breakdown"}, {"type": "paragraph", "text": "• High: {{ $node['Analyze Daily Activity'].json.priorities.high }}\\n• Medium: {{ $node['Analyze Daily Activity'].json.priorities.medium }}\\n• Low: {{ $node['Analyze Daily Activity'].json.priorities.low }}"}, {"type": "heading_3", "text": "📱 Source Distribution"}, {"type": "paragraph", "text": "• Slack: {{ $node['Analyze Daily Activity'].json.sources.slack }}\\n• Email: {{ $node['Analyze Daily Activity'].json.sources.email }}\\n• Other: {{ $node['Analyze Daily Activity'].json.sources.other }}"}, {"type": "heading_3", "text": "🤖 AI Summary"}, {"type": "paragraph", "text": "{{ $node['Generate AI Summary'].json.response }}"}, {"type": "heading_3", "text": "💡 Key Insights"}, {"type": "bulleted_list_item", "text": "{{ $node['Analyze Daily Activity'].json.insights.join('\\n• ') }}"}]}}, {"id": "send-summary-to-slack", "name": "Send Su<PERSON>ry to Slack", "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1100, 300], "parameters": {"resource": "message", "operation": "post", "select": "channel", "channelId": "{{ $env.SLACK_SUMMARY_CHANNEL }}", "text": "📊 *ALSA Daily Summary - {{ $node['Analyze Daily Activity'].json.date }}*\\n\\n{{ $node['Analyze Daily Activity'].json.insights.join('\\n') }}\\n\\n📖 Full report: {{ $node['Create Summary Page'].json.url }}", "attachments": [{"color": "#36a64f", "title": "Daily Statistics", "fields": {"item": [{"title": "Total Items", "value": "{{ $node['Analyze Daily Activity'].json.totalPages }}", "short": true}, {"title": "High Priority", "value": "{{ $node['Analyze Daily Activity'].json.priorities.high }}", "short": true}, {"title": "Action Items", "value": "{{ $node['Analyze Daily Activity'].json.actionItems.length }}", "short": true}, {"title": "Avg Words", "value": "{{ $node['Analyze Daily Activity'].json.averageWordCount }}", "short": true}]}}]}, "onError": "continueRegularOutput", "retryOnFail": true, "maxTries": 2}], "connections": {"Daily Schedule": {"main": [[{"node": "Get Yesterday's Pages", "type": "main", "index": 0}]]}, "Get Yesterday's Pages": {"main": [[{"node": "Analyze Daily Activity", "type": "main", "index": 0}]]}, "Analyze Daily Activity": {"main": [[{"node": "Generate AI Summary", "type": "main", "index": 0}]]}, "Generate AI Summary": {"main": [[{"node": "Create Summary Page", "type": "main", "index": 0}]]}, "Create Summary Page": {"main": [[{"node": "Send Su<PERSON>ry to Slack", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "saveManualExecutions": true, "timezone": "Asia/Hong_Kong"}}