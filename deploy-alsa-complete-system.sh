#!/bin/bash

# ALSA Complete System Deployment Script
# Deploys all ALSA workflows and components for a full-featured automation system

set -e

# Configuration
N8N_URL="http://localhost:5678"
N8N_USER="Paul"
N8N_PASSWORD="Super-safe-password1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Workflow files to deploy
declare -A WORKFLOWS=(
    ["alsa-slack-notion-workflow.json"]="Core ALSA Slack-to-Notion Automation"
    ["alsa-enhanced-workflow.json"]="Enhanced ALSA with AI Analysis"
    ["email-to-notion-workflow.json"]="Email-to-Notion Integration"
    ["alsa-daily-summary-workflow.json"]="Daily Summary & Analytics"
    ["alsa-analytics-api-workflow.json"]="Analytics API Endpoint"
    ["alsa-ai-enhancement-workflow.json"]="AI Content Enhancement"
    ["alsa-api-gateway-workflow.json"]="API Gateway & External Access"
    ["alsa-security-audit-workflow.json"]="Security & Audit System"
)

echo -e "${BLUE}🚀 ALSA Complete System Deployment${NC}"
echo "===================================="
echo -e "${CYAN}Deploying comprehensive ALSA automation system...${NC}"
echo ""

# Function to display banner
show_banner() {
    echo -e "${PURPLE}"
    echo "    ___    __    _____   ___ "
    echo "   /   |  / /   / ___/  /   |"
    echo "  / /| | / /    \__ \  / /| |"
    echo " / ___ |/ /___ ___/ / / ___ |"
    echo "/_/  |_/_____//____/ /_/  |_|"
    echo ""
    echo "Automated Learning & Synthesis Architect"
    echo -e "${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    echo -e "${YELLOW}Checking prerequisites...${NC}"
    
    # Check if n8n is running
    if ! curl -s "${N8N_URL}/healthz" | grep -q "ok"; then
        echo -e "${RED}❌ n8n is not running at ${N8N_URL}${NC}"
        echo -e "${YELLOW}Please start n8n first: docker-compose --profile cpu up -d${NC}"
        exit 1
    fi
    
    # Check if Ollama is running
    if ! curl -s "http://localhost:11434/api/tags" >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Ollama not detected. AI features may not work.${NC}"
        echo -e "${YELLOW}Start Ollama: docker-compose --profile cpu up ollama-cpu${NC}"
    else
        echo -e "${GREEN}✅ Ollama is running${NC}"
    fi
    
    # Check required files
    local missing_files=()
    for workflow_file in "${!WORKFLOWS[@]}"; do
        if [ ! -f "$workflow_file" ]; then
            missing_files+=("$workflow_file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        echo -e "${RED}❌ Missing workflow files:${NC}"
        for file in "${missing_files[@]}"; do
            echo -e "   - ${file}"
        done
        exit 1
    fi
    
    echo -e "${GREEN}✅ All prerequisites met${NC}"
}

# Function to setup environment
setup_environment() {
    echo -e "${YELLOW}Setting up environment...${NC}"
    
    # Create comprehensive .env template if it doesn't exist
    if [ ! -f .env ]; then
        cat > .env << 'EOF'
# Core ALSA Configuration
NOTION_API_TOKEN=your_notion_integration_token
NOTION_INBOX_PAGE_ID=your_inbox_page_id
NOTION_CATEGORIES_DB_ID=your_categories_database_id
NOTION_SUMMARIES_PAGE_ID=your_summaries_page_id

# Slack Configuration
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your_slack_signing_secret
SLACK_SUMMARY_CHANNEL=#alsa-summaries
SLACK_SECURITY_CHANNEL=#security-alerts
SLACK_ENHANCEMENTS_CHANNEL=#ai-enhancements

# Email Configuration (Optional)
ALSA_EMAIL_FROM=<EMAIL>
EMAIL_SMTP_HOST=smtp.yourdomain.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USER=your_email_user
EMAIL_SMTP_PASSWORD=your_email_password

# API Configuration
ALSA_API_KEY=your_secure_api_key_here
ALSA_MOBILE_API_KEY=your_mobile_api_key_here
ALSA_INTEGRATION_API_KEY=your_integration_api_key_here

# AI Configuration
OLLAMA_HOST=http://ollama:11434
ALSA_PROCESS_BOT_MESSAGES=false

# Security Configuration
ALSA_ENABLE_AUDIT_LOGGING=true
ALSA_SECURITY_ALERTS=true
ALSA_AUTO_BLOCK_THREATS=true

# Performance Configuration
ALSA_CACHE_ENABLED=true
ALSA_BATCH_SIZE=50
ALSA_RATE_LIMIT_PER_MINUTE=100
EOF
        echo -e "${GREEN}✅ Created .env template${NC}"
        echo -e "${YELLOW}📝 Please edit .env file with your actual credentials${NC}"
    fi
    
    # Create logs directory
    mkdir -p logs
    mkdir -p backups
    
    echo -e "${GREEN}✅ Environment setup complete${NC}"
}

# Function to authenticate with n8n
authenticate_n8n() {
    echo -e "${YELLOW}Authenticating with n8n...${NC}"
    
    # Try to get authentication cookie
    curl -s -c cookies.txt "${N8N_URL}/" >/dev/null 2>&1 || true
    
    echo -e "${GREEN}✅ Authentication ready${NC}"
}

# Function to deploy a single workflow
deploy_workflow() {
    local workflow_file=$1
    local workflow_name=$2
    local workflow_id=""
    
    echo -e "${CYAN}Deploying: ${workflow_name}${NC}"
    
    # Deploy workflow
    local response=$(curl -s -b cookies.txt \
        -X POST "${N8N_URL}/rest/workflows" \
        -H "Content-Type: application/json" \
        -H "Authorization: Basic $(echo -n "${N8N_USER}:${N8N_PASSWORD}" | base64)" \
        -d @"${workflow_file}" \
        2>/dev/null || echo "")
    
    if echo "$response" | grep -q '"id"'; then
        workflow_id=$(echo "$response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        echo -e "${GREEN}  ✅ Deployed successfully (ID: ${workflow_id})${NC}"
        
        # Activate the workflow
        curl -s -b cookies.txt \
            -X POST "${N8N_URL}/rest/workflows/${workflow_id}/activate" \
            -H "Authorization: Basic $(echo -n "${N8N_USER}:${N8N_PASSWORD}" | base64)" \
            >/dev/null 2>&1 && echo -e "${GREEN}  ✅ Activated${NC}"
        
        return 0
    else
        echo -e "${RED}  ❌ Deployment failed${NC}"
        echo -e "${RED}     Response: $response${NC}"
        return 1
    fi
}

# Function to deploy all workflows
deploy_all_workflows() {
    echo -e "${BLUE}Deploying ALSA workflows...${NC}"
    echo ""
    
    local success_count=0
    local total_count=${#WORKFLOWS[@]}
    
    for workflow_file in "${!WORKFLOWS[@]}"; do
        local workflow_name="${WORKFLOWS[$workflow_file]}"
        
        if deploy_workflow "$workflow_file" "$workflow_name"; then
            ((success_count++))
        fi
        
        echo ""
    done
    
    echo -e "${BLUE}Deployment Summary:${NC}"
    echo -e "${GREEN}✅ Successfully deployed: ${success_count}/${total_count}${NC}"
    
    if [ $success_count -eq $total_count ]; then
        echo -e "${GREEN}🎉 All workflows deployed successfully!${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  Some workflows failed to deploy${NC}"
        return 1
    fi
}

# Function to create backup
create_backup() {
    echo -e "${YELLOW}Creating backup...${NC}"
    
    local backup_dir="backups/alsa-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup workflow files
    cp *.json "$backup_dir/" 2>/dev/null || true
    
    # Backup configuration
    cp .env "$backup_dir/" 2>/dev/null || true
    
    # Create backup manifest
    cat > "$backup_dir/manifest.txt" << EOF
ALSA System Backup
Created: $(date)
Workflows: ${#WORKFLOWS[@]}
Files: $(ls -1 "$backup_dir"/*.json 2>/dev/null | wc -l)
EOF
    
    echo -e "${GREEN}✅ Backup created: ${backup_dir}${NC}"
}

# Function to show system status
show_system_status() {
    echo -e "${BLUE}ALSA System Status${NC}"
    echo "=================="
    
    # Check n8n health
    if curl -s "${N8N_URL}/healthz" | grep -q "ok"; then
        echo -e "${GREEN}✅ n8n: Running${NC}"
    else
        echo -e "${RED}❌ n8n: Not accessible${NC}"
    fi
    
    # Check Ollama
    if curl -s "http://localhost:11434/api/tags" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Ollama: Running${NC}"
    else
        echo -e "${YELLOW}⚠️  Ollama: Not detected${NC}"
    fi
    
    # Check workflows (mock)
    echo -e "${GREEN}✅ Workflows: ${#WORKFLOWS[@]} available${NC}"
    
    # Show webhook URLs
    echo ""
    echo -e "${BLUE}Webhook Endpoints:${NC}"
    echo -e "${CYAN}Core ALSA:${NC} ${N8N_URL}/webhook/slack-to-notion"
    echo -e "${CYAN}Enhanced:${NC} ${N8N_URL}/webhook/alsa-enhanced"
    echo -e "${CYAN}Analytics:${NC} ${N8N_URL}/webhook/alsa-analytics"
    echo -e "${CYAN}API Gateway:${NC} ${N8N_URL}/webhook/alsa/api/v1"
    echo -e "${CYAN}Enhancement:${NC} ${N8N_URL}/webhook/alsa-enhance"
    echo -e "${CYAN}Security:${NC} ${N8N_URL}/webhook/alsa-security"
}

# Function to show next steps
show_next_steps() {
    echo ""
    echo -e "${BLUE}🎯 Next Steps${NC}"
    echo "============="
    echo "1. 📝 Configure your .env file with actual API credentials"
    echo "2. 🔗 Set up Slack app with webhook URLs"
    echo "3. 📄 Configure Notion integration and get page/database IDs"
    echo "4. 🧪 Test the system with a simple Slack message"
    echo "5. 📊 Access analytics dashboard: file://$(pwd)/alsa-analytics-dashboard.html"
    echo "6. 🔒 Review security settings and alerts"
    echo "7. 📖 Read the complete setup guide: ALSA_SETUP_GUIDE.md"
    echo ""
    echo -e "${GREEN}🚀 ALSA is ready to transform your knowledge management!${NC}"
}

# Function to run health checks
run_health_checks() {
    echo -e "${YELLOW}Running health checks...${NC}"
    
    # Test basic connectivity
    local health_score=0
    local total_checks=5
    
    # Check n8n
    if curl -s "${N8N_URL}/healthz" | grep -q "ok"; then
        echo -e "${GREEN}✅ n8n connectivity${NC}"
        ((health_score++))
    else
        echo -e "${RED}❌ n8n connectivity${NC}"
    fi
    
    # Check Ollama
    if curl -s "http://localhost:11434/api/tags" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Ollama connectivity${NC}"
        ((health_score++))
    else
        echo -e "${YELLOW}⚠️  Ollama connectivity${NC}"
    fi
    
    # Check workflow files
    if [ ${#WORKFLOWS[@]} -eq $(ls -1 *.json 2>/dev/null | wc -l) ]; then
        echo -e "${GREEN}✅ Workflow files${NC}"
        ((health_score++))
    else
        echo -e "${RED}❌ Workflow files${NC}"
    fi
    
    # Check environment file
    if [ -f .env ]; then
        echo -e "${GREEN}✅ Environment configuration${NC}"
        ((health_score++))
    else
        echo -e "${YELLOW}⚠️  Environment configuration${NC}"
    fi
    
    # Check directories
    if [ -d logs ] && [ -d backups ]; then
        echo -e "${GREEN}✅ Directory structure${NC}"
        ((health_score++))
    else
        echo -e "${YELLOW}⚠️  Directory structure${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}Health Score: ${health_score}/${total_checks}${NC}"
    
    if [ $health_score -eq $total_checks ]; then
        echo -e "${GREEN}🎉 System is healthy and ready!${NC}"
    elif [ $health_score -ge 3 ]; then
        echo -e "${YELLOW}⚠️  System is mostly ready, some issues detected${NC}"
    else
        echo -e "${RED}❌ System has significant issues${NC}"
    fi
}

# Main execution
main() {
    show_banner
    
    echo -e "${CYAN}Starting comprehensive ALSA deployment...${NC}"
    echo ""
    
    # Run all setup steps
    check_prerequisites
    echo ""
    
    setup_environment
    echo ""
    
    authenticate_n8n
    echo ""
    
    create_backup
    echo ""
    
    deploy_all_workflows
    echo ""
    
    run_health_checks
    echo ""
    
    show_system_status
    echo ""
    
    show_next_steps
    
    # Cleanup
    rm -f cookies.txt
    
    echo ""
    echo -e "${GREEN}🎊 ALSA Complete System Deployment Finished! 🎊${NC}"
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "status")
        show_system_status
        ;;
    "health")
        run_health_checks
        ;;
    "backup")
        create_backup
        ;;
    "help")
        echo "ALSA Deployment Script"
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  deploy  - Deploy complete ALSA system (default)"
        echo "  status  - Show system status"
        echo "  health  - Run health checks"
        echo "  backup  - Create backup"
        echo "  help    - Show this help"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
